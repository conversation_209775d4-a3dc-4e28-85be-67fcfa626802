'use client';

import { motion } from 'framer-motion';
import { Star } from 'lucide-react';

interface UserRatingBadgeProps {
  rating?: number;
  userCount?: string;
  className?: string;
}

export default function UserRatingBadge({ 
  rating = 4.3, 
  userCount = "10K+", 
  className = "" 
}: UserRatingBadgeProps) {
  // Generate user avatars with different colors
  const userAvatars = [
    { initial: 'S', color: 'bg-pink-500' },
    { initial: 'A', color: 'bg-purple-500' },
    { initial: 'R', color: 'bg-pink-400' },
    { initial: 'M', color: 'bg-purple-400' },
    { initial: 'P', color: 'bg-pink-600' },
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.2 }}
      className={`inline-flex items-center gap-3 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20 ${className}`}
    >
      {/* User Avatars */}
      <div className="flex -space-x-2">
        {userAvatars.map((user, index) => (
          <motion.div
            key={index}
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ 
              duration: 0.4, 
              delay: 0.4 + index * 0.1,
              type: "spring",
              stiffness: 200 
            }}
            className={`w-8 h-8 rounded-full ${user.color} flex items-center justify-center text-white text-xs font-semibold border-2 border-white/20 shadow-lg`}
          >
            {user.initial}
          </motion.div>
        ))}
      </div>

      {/* Rating and Text */}
      <motion.div
        initial={{ opacity: 0, x: -10 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5, delay: 0.8 }}
        className="md:flex items-center gap-1"
      >
        <div className='flex items-center gap-1'>
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ 
            duration: 0.4, 
            delay: 1.0,
            type: "spring",
            stiffness: 300 
          }}
        >
          <Star 
            className="w-4 h-4 text-yellow-400 fill-yellow-400" 
          />
        </motion.div>
        <span className="text-white font-semibold text-sm">
          {rating}
        </span>
        </div>
        <span className="text-white/70 text-sm md:ml-1">
          Loved by {userCount} users
        </span>
      </motion.div>
    </motion.div>
  );
}
