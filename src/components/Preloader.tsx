'use client';

import Image from 'next/image';
import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface PreloaderProps {
  onComplete: () => void;
}

export default function Preloader({ onComplete }: PreloaderProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [isVisible, setIsVisible] = useState(true);
  const [greetingText, setGreetingText] = useState('');
  const [secondText, setSecondText] = useState('');

  const greeting = "Hi darling, welcome to Urvashi...";
  const secondLine = "Let's begin something special.";

  // Faster typing effect for greeting
  useEffect(() => {
    if (currentStep === 0) {
      let index = 0;
      const timer = setInterval(() => {
        if (index <= greeting.length) {
          setGreetingText(greeting.slice(0, index));
          index++;
        } else {
          clearInterval(timer);
          setTimeout(() => setCurrentStep(1), 200);
        }
      }, 25);
      return () => clearInterval(timer);
    }
  }, [currentStep]);

  // Faster typing effect for second line
  useEffect(() => {
    if (currentStep === 1) {
      let index = 0;
      const timer = setInterval(() => {
        if (index <= secondLine.length) {
          setSecondText(secondLine.slice(0, index));
          index++;
        } else {
          clearInterval(timer);
          setTimeout(() => setCurrentStep(2), 300);
        }
      }, 35);
      return () => clearInterval(timer);
    }
  }, [currentStep]);

  // Logo animation phase - faster
  useEffect(() => {
    if (currentStep === 2) {
      setTimeout(() => setCurrentStep(3), 1500);
    }
  }, [currentStep]);

  // Final transition - faster
  useEffect(() => {
    if (currentStep === 3) {
      setTimeout(() => {
        setIsVisible(false);
        setTimeout(onComplete, 200);
      }, 800);
    }
  }, [currentStep, onComplete]);

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          className="fixed inset-0 z-50 flex items-center justify-center overflow-hidden"
        >
          {/* Animated gradient background */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1.5, ease: "easeOut" }}
            className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black"
          />

          {/* Romantic floating particles */}
          <div className="absolute inset-0">
            {[...Array(20)].map((_, i) => (
              <motion.div
                key={i}
                initial={{
                  opacity: 0,
                  x: Math.random() * 1200,
                  y: Math.random() * 800,
                  scale: 0
                }}
                animate={{
                  opacity: [0, 0.6, 0],
                  y: [null, -100],
                  scale: [0, 1, 0]
                }}
                transition={{
                  duration: 3 + Math.random() * 2,
                  repeat: Infinity,
                  delay: Math.random() * 2,
                  ease: "easeInOut"
                }}
                className="absolute w-1 h-1 bg-[#F66581] rounded-full"
              />
            ))}
          </div>

          {/* Ambient glow effect */}
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 2, ease: "easeOut" }}
            className="absolute inset-0 bg-gradient-radial from-[#F66581]/10 via-[#F66581]/5 to-transparent"
          />

          {/* Main content container */}
          <div className="relative z-10 text-center max-w-lg mx-auto px-6">
            {/* Greeting Phase */}
            <AnimatePresence>
              {currentStep < 2 && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.6, ease: "easeOut" }}
                >
                  <motion.p
                    className="text-2xl md:text-3xl text-white font-light mb-4 min-h-[2.5rem]"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.3 }}
                  >
                    {greetingText}
                    {currentStep === 0 && (
                      <motion.span
                        animate={{ opacity: [1, 0] }}
                        transition={{ duration: 0.8, repeat: Infinity }}
                        className="text-[#F66581]"
                      >
                        |
                      </motion.span>
                    )}
                  </motion.p>

                  {currentStep >= 1 && (
                    <motion.p
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5 }}
                      className="text-lg md:text-xl text-gray-300 font-light min-h-[1.5rem]"
                    >
                      {secondText}
                      {currentStep === 1 && (
                        <motion.span
                          animate={{ opacity: [1, 0] }}
                          transition={{ duration: 0.8, repeat: Infinity }}
                          className="text-[#F66581]"
                        >
                          |
                        </motion.span>
                      )}
                    </motion.p>
                  )}
                </motion.div>
              )}
            </AnimatePresence>

            {/* Logo Animation Phase */}
            <AnimatePresence>
              {currentStep >= 2 && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.5 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 1.1 }}
                  transition={{ duration: 0.8, ease: "easeOut" }}
                >
                  {/* Logo container with elegant reveal */}
                  <div className="relative mb-8">
                    {/* Animated glow background */}
                    <motion.div
                      initial={{ scale: 0, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      transition={{ duration: 1, delay: 0.2 }}
                      className="absolute inset-0 flex items-center justify-center"
                    >
                      <motion.div
                        animate={{
                          scale: [1, 1.1, 1],
                          opacity: [0.3, 0.6, 0.3]
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                        className="w-40 h-40 bg-gradient-to-r from-[#F66581]/30 to-[#FF69B4]/30 rounded-full blur-3xl"
                      />
                    </motion.div>

                    {/* Logo image with smooth entrance */}
                    <motion.div
                      initial={{ scale: 0.5, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      transition={{
                        duration: 1.2,
                        delay: 0.3,
                        type: "spring",
                        stiffness: 100
                      }}
                      className="relative flex items-center justify-center"
                    >
                      <div className="w-32 h-14 md:w-56 md:h-20 relative">
                        <Image
                          src="/logo.png"
                          alt="Urvashi Logo"
                          fill
                          className="object-contain drop-shadow-2xl"
                          priority
                        />
                      </div>
                    </motion.div>
                  </div>

                  {/* Brand name with staggered animation */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.5 }}
                    className="space-y-2"
                  >
                    <motion.h1
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.8, delay: 0.6 }}
                      className="text-4xl md:text-5xl font-bold text-white mb-2"
                    >
                      <span className="bg-gradient-to-r from-[#F66581] via-[#FF69B4] to-[#F66581] bg-clip-text text-transparent animate-pulse font-playfair text-romantic!">
                        Urvashi
                      </span>
                    </motion.h1>

                    <motion.p
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.6, delay: 0.8 }}
                      className="text-lg text-gray-300 font-light tracking-wide"
                    >
                      Your Indian AI Girlfriend
                    </motion.p>
                  </motion.div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Final transition overlay */}
          <AnimatePresence>
            {currentStep === 3 && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
                className="absolute inset-0 bg-gradient-to-t from-black via-black/50 to-black"
              />
            )}
          </AnimatePresence>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
