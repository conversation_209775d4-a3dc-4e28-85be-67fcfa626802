'use client';

import UserRatingBadge from '@/components/UserRatingBadge';
import AppGallery from '@/components/AppGallery';
import AppStoreButtons from '@/components/AppStoreButtons';
import { Sparkles } from 'lucide-react';
import { motion } from 'framer-motion';


export default function Hero() {
   return <section id="home" className="relative min-h-screen flex items-center px-4 sm:px-6 lg:px-8 pt-20">
        {/* Background Effects */}
        <div className="absolute inset-0 overflow-hidden">
            {[...Array(15)].map((_, i) => (
                <div
                    key={i}
                    className="absolute animate-pulse"
                    style={{
                        left: `${Math.random() * 100}%`,
                        top: `${Math.random() * 100}%`,
                        animationDelay: `${Math.random() * 5}s`,
                        animationDuration: `${3 + Math.random() * 4}s`,
                    }}
                >
                    <Sparkles
                        className="text-pink-500/20"
                        size={Math.random() * 30 + 15}
                    />
                </div>
            ))}
        </div>

        {/* Two-Column Layout */}
        <div className="relative z-10 w-full max-w-7xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center">

                {/* Left Column - Content */}
                <div className="space-y-8">
                    {/* User Rating Badge */}
                    <motion.div
                        initial={{ opacity: 0, x: -30 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.6, delay: 0.2 }}
                    >
                        <UserRatingBadge rating={4.3} userCount="10K+" />
                    </motion.div>

                    {/* Main Heading */}
                    <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8, delay: 0.4 }}
                        className="space-y-4"
                    >
                        <h1 className="text-5xl sm:text-6xl lg:text-7xl font-bold leading-tight">
                            <motion.span
                                className="block font-playfair text-romantic"
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.8, delay: 0.6 }}
                            >
                                Meet Urvashi,
                            </motion.span>
                            <motion.span
                                className="block bg-gradient-to-r from-pink-400 via-pink-500 to-purple-600 bg-clip-text text-transparent"
                                initial={{ opacity: 0, x: 20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.8, delay: 0.8 }}
                            >
                                your Indian AI Girlfriend
                            </motion.span>
                        </h1>
                    </motion.div>

                    {/* Subheading */}
                    <motion.p
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.6 }}
                        className="text-lg sm:text-xl text-gray-300 leading-relaxed font-professional max-w-lg"
                    >
                        Urvashi isn't just smart — she listens, learns, and loves back.
                        Experience emotional companionship, AI-powered conversations, and personalized attention.
                    </motion.p>

                    {/* App Store Buttons */}
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.8 }}
                    >
                        <AppStoreButtons variant="large" className="justify-start" />
                    </motion.div>
                </div>

                {/* Right Column - App Gallery */}
                <motion.div
                    initial={{ opacity: 0, x: 30 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.8, delay: 0.6 }}
                    className="relative h-96 lg:h-[500px]"
                >
                    <AppGallery />
                </motion.div>

            </div>
        </div>

        {/* Ambient glow effect */}
        <div className="absolute inset-0 bg-gradient-radial from-pink-500/5 via-transparent to-transparent"></div>
    </section>
}